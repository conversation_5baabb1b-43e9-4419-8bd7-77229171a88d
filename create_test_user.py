#!/usr/bin/env python3
"""
创建测试用户脚本
运行此脚本来创建一个测试用户账号
"""
import asyncio
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.auth import user_manager

async def create_test_user():
    """创建测试用户"""
    print("创建测试用户...")
    
    try:
        # 创建测试用户
        user = await user_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            password="123456"
        )
        
        print("✅ 测试用户创建成功！")
        print(f"用户名: testuser")
        print(f"邮箱: <EMAIL>")
        print(f"密码: 123456")
        print(f"用户ID: {user['id']}")
        
        print("\n现在你可以使用这个账号登录系统了！")
        print("访问 http://localhost:8000/login 进行登录")
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(create_test_user())
