[project]
name = "easy-comments"
version = "0.1.0"
description = "Student Comments Generator with Admin System"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    "requests>=2.31.0",
    "supabase>=2.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "jinja2>=3.1.0",
]
