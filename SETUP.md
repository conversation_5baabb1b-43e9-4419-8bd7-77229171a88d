# 学生评语生成系统 - 设置指南

## 🚀 快速开始

### 1. 安装依赖

```bash
# 确保已安装 uv
uv sync
```

### 2. 配置 Supabase

1. 访问 [Supabase](https://supabase.com) 并创建新项目
2. 在项目设置中获取以下信息：
   - Project URL
   - Anon key
   - Service role key

### 3. 配置环境变量

复制并编辑环境变量文件：

```bash
cp .env.example .env
```

在 `.env` 文件中填入你的配置：

```env
# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# Deepseek API 配置
DEEPSEEK_API_KEY=your-deepseek-api-key

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password

# JWT 密钥（生产环境请使用强密钥）
JWT_SECRET_KEY=your-super-secret-jwt-key
```

### 4. 创建数据库表

在 Supabase 控制台的 SQL 编辑器中执行以下 SQL：

```sql
-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE
);

-- Token 使用记录表
CREATE TABLE IF NOT EXISTS token_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tokens_used INTEGER NOT NULL,
    api_call_type VARCHAR(50) NOT NULL DEFAULT 'comment_generation',
    request_data JSONB,
    response_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_created_at ON token_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 5. 启动应用

使用快速启动脚本：

```bash
python start.py
```

或者手动启动：

```bash
# 创建测试用户
python create_test_user.py

# 启动服务器
uv run uvicorn src.app:app --reload
```

### 6. 访问系统

- **用户登录**: http://localhost:8000/login
- **管理后台**: http://localhost:8000/admin/

默认测试账号：
- 用户名: `testuser`
- 密码: `123456`

默认管理员账号：
- 用户名: `admin`
- 密码: `admin123` (可在 .env 中修改)

## 🔧 功能说明

### 用户功能
1. 登录系统
2. 填写学生信息
3. 生成 AI 评语
4. 查看生成历史

### 管理员功能
1. 用户管理（创建、编辑、删除）
2. Token 使用统计
3. 系统监控
4. 数据分析

## 🛠️ 开发说明

### 项目结构
```
src/
├── app.py              # 主应用
├── auth.py             # 认证模块
├── database.py         # 数据库配置
├── token_tracker.py    # Token 追踪
└── admin_routes.py     # 管理路由

templates/
├── admin/              # 管理后台模板
└── login.html          # 登录页面
```

### 自定义字段
在 `src/app.py` 中修改 `fields` 列表来自定义评语字段。

### API 集成
替换 `DEEPSEEK_API_URL` 和相关代码来集成其他 AI 服务。

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 Supabase URL 和密钥
   - 确认网络连接

2. **认证失败**
   - 检查 JWT 密钥配置
   - 清除浏览器 Cookie

3. **API 调用失败**
   - 检查 Deepseek API 密钥
   - 确认 API 配额

### 日志查看
应用日志会显示在控制台，包含详细的错误信息。

## 📝 生产部署

1. 使用强密码和密钥
2. 设置 `DEBUG=False`
3. 配置 HTTPS
4. 使用生产级数据库
5. 设置监控和备份

## 🤝 支持

如有问题，请查看：
1. 控制台日志
2. Supabase 控制台
3. 网络连接状态
4. 环境变量配置
