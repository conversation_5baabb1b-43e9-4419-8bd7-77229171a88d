#!/usr/bin/env python3
"""
快速启动脚本
"""
import asyncio
import sys
import os
import subprocess

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_env_file():
    """检查环境变量文件"""
    if not os.path.exists('.env'):
        print("❌ 未找到 .env 文件")
        print("请先复制 .env.example 为 .env 并配置环境变量")
        print("cp .env.example .env")
        return False
    return True

async def init_db():
    """初始化数据库"""
    print("🔧 初始化数据库...")
    try:
        from src.database import init_database
        success = await init_database()
        if success:
            print("✅ 数据库初始化成功")
            return True
        else:
            print("❌ 数据库初始化失败")
            return False
    except Exception as e:
        print(f"❌ 数据库初始化错误: {e}")
        return False

async def create_test_user_if_needed():
    """如果需要，创建测试用户"""
    try:
        from src.auth import user_manager
        
        # 检查是否已有用户
        users_data = await user_manager.get_all_users(limit=1)
        if users_data["total"] > 0:
            print("✅ 已存在用户，跳过创建测试用户")
            return True
        
        print("👤 创建测试用户...")
        user = await user_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            password="123456"
        )
        
        print("✅ 测试用户创建成功！")
        print("   用户名: testuser")
        print("   密码: 123456")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    print("访问地址:")
    print("  用户登录: http://localhost:8000/login")
    print("  管理后台: http://localhost:8000/admin/")
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        subprocess.run([
            "uv", "run", "uvicorn", "src.app:app", 
            "--reload", "--host", "0.0.0.0", "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except FileNotFoundError:
        print("❌ 未找到 uv 命令，尝试使用 python 启动...")
        try:
            subprocess.run([
                "python", "-m", "uvicorn", "src.app:app",
                "--reload", "--host", "0.0.0.0", "--port", "8000"
            ])
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")

async def main():
    """主函数"""
    print("🎓 学生评语生成系统 - 快速启动")
    print("=" * 40)
    
    # 检查环境文件
    if not check_env_file():
        return
    
    # 初始化数据库
    if not await init_db():
        return
    
    # 创建测试用户
    if not await create_test_user_if_needed():
        return
    
    print("\n🎉 系统准备就绪！")
    print("=" * 40)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    asyncio.run(main())
