"""
Token 使用统计和追踪模块
"""
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from .database import get_supabase_admin_client

class TokenTracker:
    """Token 使用追踪器"""
    
    def __init__(self):
        self.supabase = get_supabase_admin_client()
    
    async def record_token_usage(
        self, 
        user_id: str, 
        tokens_used: int, 
        api_call_type: str = "comment_generation",
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """记录 Token 使用"""
        try:
            usage_data = {
                "user_id": user_id,
                "tokens_used": tokens_used,
                "api_call_type": api_call_type,
                "request_data": request_data,
                "response_data": response_data
            }
            
            result = self.supabase.table("token_usage").insert(usage_data).execute()
            return len(result.data) > 0
            
        except Exception as e:
            print(f"记录 Token 使用失败: {e}")
            return False
    
    async def get_user_token_usage(
        self, 
        user_id: str, 
        days: int = 30
    ) -> Dict[str, Any]:
        """获取用户 Token 使用统计"""
        try:
            # 计算时间范围
            start_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
            
            # 获取用户的 Token 使用记录
            result = self.supabase.table("token_usage").select("*").eq(
                "user_id", user_id
            ).gte("created_at", start_date).execute()
            
            usage_records = result.data
            
            # 计算统计信息
            total_tokens = sum(record["tokens_used"] for record in usage_records)
            total_calls = len(usage_records)
            
            # 按日期分组统计
            daily_usage = {}
            for record in usage_records:
                date = record["created_at"][:10]  # 获取日期部分
                if date not in daily_usage:
                    daily_usage[date] = {"tokens": 0, "calls": 0}
                daily_usage[date]["tokens"] += record["tokens_used"]
                daily_usage[date]["calls"] += 1
            
            # 按 API 调用类型分组统计
            usage_by_type = {}
            for record in usage_records:
                call_type = record["api_call_type"]
                if call_type not in usage_by_type:
                    usage_by_type[call_type] = {"tokens": 0, "calls": 0}
                usage_by_type[call_type]["tokens"] += record["tokens_used"]
                usage_by_type[call_type]["calls"] += 1
            
            return {
                "user_id": user_id,
                "period_days": days,
                "total_tokens": total_tokens,
                "total_calls": total_calls,
                "average_tokens_per_call": total_tokens / total_calls if total_calls > 0 else 0,
                "daily_usage": daily_usage,
                "usage_by_type": usage_by_type,
                "recent_records": usage_records[-10:]  # 最近 10 条记录
            }
            
        except Exception as e:
            print(f"获取用户 Token 使用统计失败: {e}")
            return {
                "user_id": user_id,
                "period_days": days,
                "total_tokens": 0,
                "total_calls": 0,
                "average_tokens_per_call": 0,
                "daily_usage": {},
                "usage_by_type": {},
                "recent_records": []
            }
    
    async def get_all_users_token_usage(
        self, 
        days: int = 30,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """获取所有用户的 Token 使用统计"""
        try:
            start_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
            
            # 获取所有用户的 Token 使用记录
            result = self.supabase.table("token_usage").select(
                "user_id, tokens_used, api_call_type, created_at"
            ).gte("created_at", start_date).range(offset, offset + limit - 1).execute()
            
            usage_records = result.data
            
            # 按用户分组统计
            user_stats = {}
            for record in usage_records:
                user_id = record["user_id"]
                if user_id not in user_stats:
                    user_stats[user_id] = {
                        "total_tokens": 0,
                        "total_calls": 0,
                        "last_used": None
                    }
                
                user_stats[user_id]["total_tokens"] += record["tokens_used"]
                user_stats[user_id]["total_calls"] += 1
                
                # 更新最后使用时间
                if (user_stats[user_id]["last_used"] is None or 
                    record["created_at"] > user_stats[user_id]["last_used"]):
                    user_stats[user_id]["last_used"] = record["created_at"]
            
            # 获取用户信息
            if user_stats:
                user_ids = list(user_stats.keys())
                users_result = self.supabase.table("users").select(
                    "id, username, email"
                ).in_("id", user_ids).execute()
                
                users_info = {user["id"]: user for user in users_result.data}
                
                # 合并用户信息和统计数据
                for user_id, stats in user_stats.items():
                    if user_id in users_info:
                        stats.update(users_info[user_id])
            
            # 计算总体统计
            total_tokens = sum(record["tokens_used"] for record in usage_records)
            total_calls = len(usage_records)
            
            return {
                "period_days": days,
                "total_tokens": total_tokens,
                "total_calls": total_calls,
                "total_users": len(user_stats),
                "user_stats": user_stats,
                "average_tokens_per_user": total_tokens / len(user_stats) if user_stats else 0,
                "average_calls_per_user": total_calls / len(user_stats) if user_stats else 0
            }
            
        except Exception as e:
            print(f"获取所有用户 Token 使用统计失败: {e}")
            return {
                "period_days": days,
                "total_tokens": 0,
                "total_calls": 0,
                "total_users": 0,
                "user_stats": {},
                "average_tokens_per_user": 0,
                "average_calls_per_user": 0
            }
    
    async def get_token_usage_trends(self, days: int = 30) -> Dict[str, Any]:
        """获取 Token 使用趋势"""
        try:
            start_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
            
            result = self.supabase.table("token_usage").select(
                "tokens_used, created_at"
            ).gte("created_at", start_date).execute()
            
            usage_records = result.data
            
            # 按日期分组统计
            daily_trends = {}
            for record in usage_records:
                date = record["created_at"][:10]
                if date not in daily_trends:
                    daily_trends[date] = {"tokens": 0, "calls": 0}
                daily_trends[date]["tokens"] += record["tokens_used"]
                daily_trends[date]["calls"] += 1
            
            # 排序日期
            sorted_dates = sorted(daily_trends.keys())
            
            return {
                "period_days": days,
                "daily_trends": daily_trends,
                "dates": sorted_dates,
                "total_days_with_usage": len(sorted_dates)
            }
            
        except Exception as e:
            print(f"获取 Token 使用趋势失败: {e}")
            return {
                "period_days": days,
                "daily_trends": {},
                "dates": [],
                "total_days_with_usage": 0
            }

# 估算 Token 使用量的辅助函数
def estimate_tokens(text: str) -> int:
    """
    估算文本的 Token 数量
    这是一个简单的估算，实际 Token 数量可能有所不同
    """
    # 简单估算：中文字符约 1.5 tokens，英文单词约 1.3 tokens
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    english_words = len([w for w in text.split() if any(c.isalpha() for c in w)])
    other_chars = len(text) - chinese_chars - sum(len(w) for w in text.split() if any(c.isalpha() for c in w))
    
    estimated_tokens = int(chinese_chars * 1.5 + english_words * 1.3 + other_chars * 0.5)
    return max(estimated_tokens, 1)  # 至少 1 个 token

# 创建全局实例
token_tracker = TokenTracker()
