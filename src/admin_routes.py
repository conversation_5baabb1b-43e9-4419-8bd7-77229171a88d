"""
后台管理系统路由
"""
from fastapi import APIRouter, HTTPException, status, Depends, Form, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from typing import Optional
from datetime import timed<PERSON><PERSON>
from .auth import verify_admin_credentials, auth_manager, user_manager
from .token_tracker import token_tracker

# 创建路由器
admin_router = APIRouter(prefix="/admin", tags=["admin"])

# 模板配置
templates = Jinja2Templates(directory="templates")

# 管理员会话存储（简单实现，生产环境建议使用 Redis）
admin_sessions = {}

def check_admin_session(request: Request) -> bool:
    """检查管理员会话"""
    session_token = request.cookies.get("admin_session")
    return session_token in admin_sessions

@admin_router.get("/", response_class=HTMLResponse)
async def admin_login_page(request: Request):
    """管理员登录页面"""
    if check_admin_session(request):
        return RedirectResponse(url="/admin/dashboard", status_code=302)
    
    return templates.TemplateResponse("admin/login.html", {"request": request})

@admin_router.post("/login")
async def admin_login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...)
):
    """管理员登录"""
    if not await verify_admin_credentials(username, password):
        return templates.TemplateResponse(
            "admin/login.html", 
            {"request": request, "error": "用户名或密码错误"}
        )
    
    # 创建会话
    session_token = auth_manager.create_access_token(
        data={"sub": "admin", "type": "admin"},
        expires_delta=timedelta(hours=8)
    )
    
    admin_sessions[session_token] = {"username": username}
    
    response = RedirectResponse(url="/admin/dashboard", status_code=302)
    response.set_cookie(
        key="admin_session", 
        value=session_token, 
        max_age=8*3600,  # 8 小时
        httponly=True
    )
    
    return response

@admin_router.get("/logout")
async def admin_logout(request: Request):
    """管理员登出"""
    session_token = request.cookies.get("admin_session")
    if session_token in admin_sessions:
        del admin_sessions[session_token]
    
    response = RedirectResponse(url="/admin/", status_code=302)
    response.delete_cookie("admin_session")
    return response

@admin_router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """管理员仪表板"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    # 获取统计数据
    users_data = await user_manager.get_all_users(limit=10)
    token_stats = await token_tracker.get_all_users_token_usage(days=30)
    trends = await token_tracker.get_token_usage_trends(days=7)
    
    return templates.TemplateResponse("admin/dashboard.html", {
        "request": request,
        "users_count": users_data["total"],
        "recent_users": users_data["users"],
        "token_stats": token_stats,
        "trends": trends
    })

@admin_router.get("/users", response_class=HTMLResponse)
async def admin_users(request: Request, page: int = 1, limit: int = 20):
    """用户管理页面"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    offset = (page - 1) * limit
    users_data = await user_manager.get_all_users(limit=limit, offset=offset)
    
    return templates.TemplateResponse("admin/users.html", {
        "request": request,
        "users": users_data["users"],
        "total": users_data["total"],
        "page": page,
        "limit": limit,
        "total_pages": (users_data["total"] + limit - 1) // limit
    })

@admin_router.get("/users/create", response_class=HTMLResponse)
async def admin_create_user_page(request: Request):
    """创建用户页面"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    return templates.TemplateResponse("admin/create_user.html", {"request": request})

@admin_router.post("/users/create")
async def admin_create_user(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...)
):
    """创建用户"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    try:
        user = await user_manager.create_user(username, email, password)
        return RedirectResponse(url="/admin/users", status_code=302)
    except HTTPException as e:
        return templates.TemplateResponse("admin/create_user.html", {
            "request": request,
            "error": e.detail
        })

@admin_router.get("/users/{user_id}/edit", response_class=HTMLResponse)
async def admin_edit_user_page(request: Request, user_id: str):
    """编辑用户页面"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    user = await user_manager.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return templates.TemplateResponse("admin/edit_user.html", {
        "request": request,
        "user": user
    })

@admin_router.post("/users/{user_id}/edit")
async def admin_edit_user(
    request: Request,
    user_id: str,
    username: str = Form(...),
    email: str = Form(...),
    is_active: bool = Form(False),
    password: Optional[str] = Form(None)
):
    """编辑用户"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    update_data = {
        "username": username,
        "email": email,
        "is_active": is_active
    }
    
    if password and password.strip():
        update_data["password"] = password
    
    user = await user_manager.update_user(user_id, update_data)
    if user:
        return RedirectResponse(url="/admin/users", status_code=302)
    else:
        return templates.TemplateResponse("admin/edit_user.html", {
            "request": request,
            "user": await user_manager.get_user_by_id(user_id),
            "error": "更新用户失败"
        })

@admin_router.post("/users/{user_id}/delete")
async def admin_delete_user(request: Request, user_id: str):
    """删除用户"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    success = await user_manager.delete_user(user_id)
    return RedirectResponse(url="/admin/users", status_code=302)

@admin_router.get("/token-usage", response_class=HTMLResponse)
async def admin_token_usage(request: Request, days: int = 30):
    """Token 使用统计页面"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    stats = await token_tracker.get_all_users_token_usage(days=days)
    trends = await token_tracker.get_token_usage_trends(days=days)
    
    return templates.TemplateResponse("admin/token_usage.html", {
        "request": request,
        "stats": stats,
        "trends": trends,
        "days": days
    })

@admin_router.get("/token-usage/{user_id}", response_class=HTMLResponse)
async def admin_user_token_usage(request: Request, user_id: str, days: int = 30):
    """用户 Token 使用详情页面"""
    if not check_admin_session(request):
        return RedirectResponse(url="/admin/", status_code=302)
    
    user = await user_manager.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    usage_stats = await token_tracker.get_user_token_usage(user_id, days=days)
    
    return templates.TemplateResponse("admin/user_token_usage.html", {
        "request": request,
        "user": user,
        "usage_stats": usage_stats,
        "days": days
    })
