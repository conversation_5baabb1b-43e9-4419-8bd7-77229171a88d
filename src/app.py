from fastapi import FastAPI, Request, Form, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import requests
import os
from dotenv import load_dotenv
from .auth import get_current_user, user_manager, auth_manager
from .admin_routes import admin_router
from .token_tracker import token_tracker, estimate_tokens
from .database import init_database

# 加载环境变量
load_dotenv()

# Define the application
app = FastAPI(title="EasyComments", description="基于 AI 的学生评语生成应用")

# 配置模板
templates = Jinja2Templates(directory="templates")

# 包含管理员路由
app.include_router(admin_router)

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    print("正在初始化数据库...")
    success = await init_database()
    if success:
        print("数据库初始化成功")
    else:
        print("数据库初始化失败，请检查配置")

# Load the prompt template from the file
with open("student_comment_prompt.md", "r", encoding="utf-8") as f:
    prompt_template = f.read()

# Placeholder for Deepseek API details - REPLACE WITH YOUR ACTUAL DETAILS
DEEPSEEK_API_KEY = os.environ.get("DEEPSEEK_API_KEY", "YOUR_DEEPSEEK_API_KEY") # Get from env var or replace
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions" # Deepseek V3 Chat Completions endpoint

# Define the fields based on fields_referance.md for easier iteration
fields = [
    ("学生姓名", "student_name", "text", None),
    ("学生性别", "student_gender", "enum", ["男", "女"]),
    ("学期/学年", "term_academic_year", "enum", ["2023-2024学年第一学期", "2023-2024学年第二学期", "2024-2025学年第一学期", "2024-2025学年第二学期"]),
    ("学科强项分析", "subject_strengths_analysis", "textarea", None),
    ("学科薄弱点分析", "subject_weaknesses_analysis", "textarea", None),
    ("学习潜力评估", "learning_potential_assessment", "enum", ["潜力较大", "学习能力稳定", "需要进一步激发潜力"]),
    ("对学科的兴趣程度", "level_of_interest_in_subjects", "enum", ["浓厚", "一般", "缺乏兴趣"]),
    ("课堂专注度", "classroom_concentration", "enum", ["始终高度专注", "大部分时间专注", "有时分散注意力", "容易受外界干扰"]),
    ("作业完成情况", "homework_completion", "enum", ["总是按时高质量", "大部分按时完成", "有时拖延或应付"]),
    ("学习主动性与自觉性", "learning_proactiveness_self_consciousness", "enum", ["学习主动性强", "完成要求任务", "需要督促"]),
    ("遵守纪律与规章", "compliance_with_discipline_regulations", "enum", ["严格遵守", "基本遵守", "需要加强"]),
    ("待人接物态度", "attitude_towards_others", "enum", ["有礼貌尊重他人", "基本有礼貌", "不够尊重"]),
    ("责任心", "sense_of_responsibility", "enum", ["较强", "能完成分配任务", "有待提高"]),
    ("特长与兴趣", "talents_interests", "textarea", None),
    ("教师总体评价", "overall_teacher_assessment", "textarea", None),
    ("未来发展期望", "expectations_for_future_development", "textarea", None),
    ("针对性改进建议/鼓励方向", "specific_suggestions_for_improvement_directions_for_encouragement", "textarea", None),
    ("评语", "generated_comment", "textarea", None),
]

async def generate_comment_for_student(student_data: dict, student_name_for_error: str, user_id: str = None) -> str:
    """
    Calls the Deepseek API to generate a comment for a single student.
    """
    # Construct the data string for the current student
    student_data_str = "\n".join([f"{key}: {value}" for key, value in student_data.items() if key != "generated_comment"])

    # Combine the prompt template with the current student's data
    full_prompt = f"{prompt_template}\n\n输入数据:\n{student_data_str}"

    # Prepare data for Deepseek API call
    api_payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": full_prompt}
        ],
        "max_tokens": 500,
        "temperature": 0.7
    }

    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }

    generated_comment = f"无法为学生 {student_name_for_error} 生成评语，请检查API配置和输入数据。"
    tokens_used = 0

    try:
        response = requests.post(DEEPSEEK_API_URL, json=api_payload, headers=headers)
        response.raise_for_status()

        response_json = response.json()
        if 'choices' in response_json and response_json['choices'] and 'message' in response_json['choices'][0] and 'content' in response_json['choices'][0]['message']:
             generated_comment = response_json['choices'][0]['message']['content']

             # 估算 Token 使用量
             input_tokens = estimate_tokens(full_prompt)
             output_tokens = estimate_tokens(generated_comment)
             tokens_used = input_tokens + output_tokens

        else:
             print(f"Unexpected API response structure for student {student_name_for_error}: Missing keys")
             generated_comment = f"处理AI响应失败 (学生 {student_name_for_error}): 意外的响应格式 (缺少必要的键)"

    except requests.exceptions.RequestException as e:
        print(f"API call failed for student {student_name_for_error}: {e}")
        generated_comment = f"调用AI服务失败 (学生 {student_name_for_error}): {e}"
    except Exception as e:
        print(f"An unexpected error occurred for student {student_name_for_error}: {e}")
        generated_comment = f"处理AI响应时发生意外错误 (学生 {student_name_for_error}): {e}"

    # 记录 Token 使用
    if user_id and tokens_used > 0:
        await token_tracker.record_token_usage(
            user_id=user_id,
            tokens_used=tokens_used,
            api_call_type="comment_generation",
            request_data={"student_name": student_name_for_error, "prompt_length": len(full_prompt)},
            response_data={"comment_length": len(generated_comment)}
        )

    return generated_comment

def create_student_row_html(index: int, initial_data: dict = None) -> str:
    """Generates HTML for a single student row."""
    if initial_data is None:
        initial_data = {}
    row_html = f'<tr id="student_row_{index}" class="student-row">'
    for field_display_name, field_internal_name, field_type, options in fields:
        input_name = f"{field_internal_name}_{index}"
        field_value = initial_data.get(field_internal_name, "")
        if field_internal_name == "generated_comment":
            # 评语字段不再是只读，但由AI生成
            row_html += f'<td class="field-cell"><textarea name="{input_name}" id="generated_comment_output_{index}" class="field-textarea" rows="6" readonly>{field_value}</textarea></td>'
        elif field_type == "textarea":
            row_html += f'<td class="field-cell"><textarea name="{input_name}" class="field-textarea" rows="6" placeholder="请输入{field_display_name}...">{field_value}</textarea></td>'
        elif field_type == "enum":
            row_html += f'<td class="field-cell"><select name="{input_name}" class="field-input">'
            row_html += f'<option value="">请选择{field_display_name}</option>'
            for option in options:
                selected = "selected" if option == field_value else ""
                row_html += f'<option value="{option}" {selected}>{option}</option>'
            row_html += '</select></td>'
        else:
            row_html += f'<td class="field-cell"><input type="text" name="{input_name}" class="field-input" placeholder="请输入{field_display_name}..." value="{field_value}"></td>'
    
    # Add action buttons: Generate Comment and Remove
    row_html += f'''
    <td class="action-cell">
        <button type="button" class="btn-generate-row"
                hx-post="/generate-comment-for-row/{index}"
                hx-target="#generated_comment_output_{index}"
                hx-swap="innerHTML"
                hx-indicator="#spinner_{index}">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
            </svg>
            <span id="spinner_{index}" class="htmx-indicator">
                <svg class="animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" width="16" height="16">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </span>
        </button>
        <button type="button" class="btn-remove" hx-delete="/remove-row/{index}" hx-target="#student_row_{index}" hx-swap="outerHTML">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
        </button>
    </td>
    '''
    row_html += '</tr>'
    return row_html

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页面 - 检查用户认证"""
    try:
        # 尝试获取当前用户
        current_user = await get_current_user(request)
    except HTTPException:
        # 如果未认证，重定向到登录页面
        return RedirectResponse(url="/login", status_code=302)

    return await authenticated_home(request, current_user)

async def authenticated_home(request: Request, current_user: dict):
    header_row_html = "".join([f"<th>{field[0]}</th>" for field in fields])
    header_row_html += "<th>操作</th>"

    initial_row_html = create_student_row_html(0)

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>EasyComments - 欢迎 {current_user['username']}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <script src="https://unpkg.com/htmx.org@1.9.12"></script>
        <style>
            /* 现代化CSS变量定义 - 优化配色方案 */
            :root {{
                --background: 0 0% 100%;
                --foreground: 222.2 84% 4.9%;
                --card: 0 0% 100%;
                --card-foreground: 222.2 84% 4.9%;
                --popover: 0 0% 100%;
                --popover-foreground: 222.2 84% 4.9%;
                --primary: 217 91% 60%;
                --primary-foreground: 210 40% 98%;
                --secondary: 210 40% 96%;
                --secondary-foreground: 222.2 84% 4.9%;
                --muted: 210 40% 96%;
                --muted-foreground: 215.4 16.3% 46.9%;
                --accent: 210 40% 96%;
                --accent-foreground: 222.2 84% 4.9%;
                --destructive: 0 84.2% 60.2%;
                --destructive-foreground: 210 40% 98%;
                --border: 214.3 31.8% 91.4%;
                --input: 214.3 31.8% 91.4%;
                --ring: 217 91% 60%;
                --radius: 0.75rem;
                --success: 142 76% 36%;
                --success-foreground: 355 100% 97%;
                --warning: 38 92% 50%;
                --warning-foreground: 48 96% 89%;
                --info: 199 89% 48%;
                --info-foreground: 210 40% 98%;
            }}

            * {{
                box-sizing: border-box;
            }}

            body {{
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans SC", sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 1rem;
                background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 98%) 100%);
                color: hsl(var(--foreground));
                font-size: 14px;
                min-height: 100vh;
            }}

            .container {{
                max-width: 100%;
                margin: 0 auto;
                position: relative;
            }}

            h1 {{
                font-size: 2.25rem;
                font-weight: 800;
                text-align: center;
                background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--info)) 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 1rem;
                letter-spacing: -0.025em;
            }}

            .user-info {{
                text-align: center;
                margin-bottom: 1.5rem;
                padding: 1.25rem;
                background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
                border-radius: calc(var(--radius) + 4px);
                border: 1px solid hsl(var(--border));
                box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.04);
                backdrop-filter: blur(8px);
            }}

            .user-info .username {{
                font-weight: 700;
                color: hsl(var(--primary));
                font-size: 1.1em;
            }}

            .logout-btn {{
                position: absolute;
                top: 0;
                right: 0;
                padding: 0.75rem 1.25rem;
                background: linear-gradient(135deg, hsl(var(--destructive)) 0%, hsl(0 84.2% 50%) 100%);
                color: hsl(var(--destructive-foreground));
                border: none;
                border-radius: var(--radius);
                text-decoration: none;
                font-size: 0.875rem;
                font-weight: 600;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1);
            }}

            .logout-btn:hover {{
                transform: translateY(-2px);
                box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
                filter: brightness(1.05);
            }}

            .form-container {{
                background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--background)) 100%);
                border: 1px solid hsl(var(--border));
                border-radius: calc(var(--radius) + 4px);
                padding: 1.5rem;
                box-shadow: 0 4px 16px 0 rgb(0 0 0 / 0.08), 0 2px 8px 0 rgb(0 0 0 / 0.04);
                margin-bottom: 1rem;
                height: calc(100vh - 140px);
                display: flex;
                flex-direction: column;
                backdrop-filter: blur(8px);
            }}

            .table-container {{
                overflow: auto;
                border-radius: calc(var(--radius) + 2px);
                border: 1px solid hsl(var(--border));
                margin-bottom: 1.5rem;
                flex: 1;
                max-height: calc(100vh - 220px);
                box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.06);
                background-color: hsl(var(--card));
            }}

            table {{
                width: 100%;
                border-collapse: collapse;
                font-size: 0.875rem;
                min-width: 2000px;
                background-color: hsl(var(--card));
            }}

            th {{
                background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--secondary)) 100%);
                color: hsl(var(--foreground));
                font-weight: 700;
                padding: 1.25rem 1rem;
                text-align: left;
                border-bottom: 2px solid hsl(var(--border));
                white-space: nowrap;
                font-size: 0.9rem;
                position: sticky;
                top: 0;
                z-index: 10;
                min-width: 180px;
                box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.05);
                letter-spacing: 0.025em;
            }}

            .student-row {{
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                animation: fadeIn 0.4s ease;
                border-bottom: 1px solid hsl(var(--border));
            }}

            .student-row:hover {{
                background: linear-gradient(135deg, hsl(var(--muted) / 0.3) 0%, hsl(var(--accent) / 0.2) 100%);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.06);
            }}

            .field-cell {{
                padding: 1rem 0.75rem;
                border-bottom: 1px solid hsl(var(--border) / 0.6);
                vertical-align: top;
                min-width: 280px;
                max-width: 350px;
                background-color: hsl(var(--card));
            }}

            .action-cell {{
                padding: 1rem 0.75rem;
                border-bottom: 1px solid hsl(var(--border) / 0.6);
                text-align: center;
                width: 140px;
                display: flex;
                gap: 10px;
                justify-content: center;
                align-items: center;
                background-color: hsl(var(--card));
            }}

            .field-input, .field-textarea {{
                width: 100%;
                padding: 0.875rem;
                border: 1.5px solid hsl(var(--input));
                border-radius: var(--radius);
                background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--card)) 100%);
                color: hsl(var(--foreground));
                font-size: 0.875rem;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                resize: vertical;
                min-height: 85px;
                font-family: inherit;
                box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.05);
            }}

            .field-input:focus, .field-textarea:focus {{
                outline: none;
                border-color: hsl(var(--ring));
                box-shadow: 0 0 0 3px hsl(var(--ring) / 0.15), 0 2px 8px 0 rgb(0 0 0 / 0.08);
                transform: translateY(-1px);
            }}

            .field-input::placeholder, .field-textarea::placeholder {{
                color: hsl(var(--muted-foreground) / 0.8);
                font-style: italic;
            }}

            .btn-primary, .btn-generate-row {{
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--info)) 100%);
                color: hsl(var(--primary-foreground));
                border: none;
                border-radius: var(--radius);
                font-size: 0.875rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                text-decoration: none;
                white-space: nowrap;
                box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1);
            }}

            .btn-primary:hover, .btn-generate-row:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 16px 0 rgb(0 0 0 / 0.15);
                filter: brightness(1.05);
            }}

            .btn-remove {{
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                padding: 0;
                background: linear-gradient(135deg, hsl(var(--destructive)) 0%, hsl(0 84.2% 50%) 100%);
                color: hsl(var(--destructive-foreground));
                border: none;
                border-radius: var(--radius);
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1);
            }}

            .btn-remove:hover {{
                transform: translateY(-2px) scale(1.05);
                box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
                filter: brightness(1.05);
            }}

            .btn-add {{
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                padding: 1rem 1.75rem;
                background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(142 76% 32%) 100%);
                color: hsl(var(--success-foreground));
                border: none;
                border-radius: var(--radius);
                font-size: 0.875rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                margin-right: 1rem;
                box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.1);
            }}

            .btn-add:hover {{
                transform: translateY(-2px);
                box-shadow: 0 6px 16px 0 rgb(0 0 0 / 0.15);
                filter: brightness(1.05);
            }}

            .button-group {{
                display: flex;
                gap: 1rem;
                align-items: center;
                flex-wrap: wrap;
                flex-shrink: 0;
            }}

            /* 响应式设计优化 */
            @media (max-width: 768px) {{
                body {{
                    padding: 0.5rem;
                }}

                h1 {{
                    font-size: 1.75rem;
                    margin-bottom: 0.75rem;
                }}

                .user-info {{
                    padding: 1rem;
                    margin-bottom: 1rem;
                }}

                .form-container {{
                    padding: 1rem;
                    height: calc(100vh - 100px);
                }}

                .table-container {{
                    max-height: calc(100vh - 160px);
                }}

                th, .field-cell, .action-cell {{
                    padding: 0.75rem 0.5rem;
                }}

                .field-input, .field-textarea {{
                    font-size: 0.8rem;
                    padding: 0.75rem;
                    min-height: 70px;
                }}

                .button-group {{
                    flex-direction: column;
                    align-items: stretch;
                    gap: 0.75rem;
                }}

                .btn-add, .btn-primary {{
                    width: 100%;
                    justify-content: center;
                    padding: 1rem;
                }}

                .logout-btn {{
                    position: relative;
                    top: auto;
                    right: auto;
                    margin-bottom: 1rem;
                    align-self: flex-end;
                }}
            }}

            /* 优化动画效果 */
            @keyframes fadeIn {{
                from {{
                    opacity: 0;
                    transform: translateY(20px) scale(0.95);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }}
            }}

            @keyframes slideInFromLeft {{
                from {{
                    opacity: 0;
                    transform: translateX(-30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateX(0);
                }}
            }}

            @keyframes pulse {{
                0%, 100% {{
                    opacity: 1;
                }}
                50% {{
                    opacity: 0.7;
                }}
            }}

            /* 优化滚动条样式 */
            .table-container::-webkit-scrollbar {{
                width: 14px;
                height: 14px;
            }}

            .table-container::-webkit-scrollbar-track {{
                background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--secondary)) 100%);
                border-radius: calc(var(--radius) + 2px);
                border: 1px solid hsl(var(--border));
            }}

            .table-container::-webkit-scrollbar-thumb {{
                background: linear-gradient(135deg, hsl(var(--primary) / 0.6) 0%, hsl(var(--info) / 0.6) 100%);
                border-radius: calc(var(--radius) + 2px);
                border: 2px solid hsl(var(--background));
                transition: all 0.2s ease;
            }}

            .table-container::-webkit-scrollbar-thumb:hover {{
                background: linear-gradient(135deg, hsl(var(--primary) / 0.8) 0%, hsl(var(--info) / 0.8) 100%);
                transform: scale(1.05);
            }}

            .table-container::-webkit-scrollbar-corner {{
                background: hsl(var(--muted));
                border-radius: var(--radius);
            }}

            /* 优化HTMX加载指示器样式 */
            .htmx-indicator {{
                display: none;
                animation: pulse 1.5s ease-in-out infinite;
            }}

            .htmx-request .htmx-indicator {{
                display: inline-block;
            }}

            .htmx-request.htmx-indicator {{
                display: inline-block;
            }}

            .htmx-request {{
                position: relative;
                overflow: hidden;
            }}

            .htmx-request::after {{
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.2), transparent);
                animation: shimmer 1.5s infinite;
            }}

            .animate-spin {{
                animation: spin 1s linear infinite;
            }}

            @keyframes spin {{
                from {{ transform: rotate(0deg); }}
                to {{ transform: rotate(360deg); }}
            }}

            @keyframes shimmer {{
                0% {{ left: -100%; }}
                100% {{ left: 100%; }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <a href="/admin/logout" class="logout-btn">退出登录</a>
            <h1>学生评语生成器</h1>
            <div class="user-info">
                <span>欢迎，</span><span class="username">{current_user['username']}</span>
                <span>({current_user['email']})</span>
                <a href="/admin/dashboard" style="margin-left: 1rem; color: hsl(var(--primary));">管理后台</a>
            </div>
            
            <div class="form-container">
                <form>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>{header_row_html}</tr>
                            </thead>
                            <tbody id="student_table_body">
                                {initial_row_html}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="button-group">
                        <button type="button" class="btn-add" hx-post="/add-row" hx-target="#student_table_body" hx-swap="beforeend">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                            添加学生
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <script>
            let studentCount = 1;
            const addStudentBtn = document.querySelector('.btn-add');

            // 更新学生计数
            addStudentBtn.addEventListener('htmx:beforeRequest', function(event) {{
                event.detail.parameters['studentCount'] = studentCount;
            }});

            addStudentBtn.addEventListener('htmx:afterOnLoad', function(event) {{
                studentCount++;
            }});

            // 添加表格行的淡入效果
            document.addEventListener('htmx:afterOnLoad', function(event) {{
                if (event.target.classList && event.target.classList.contains('student-row')) {{
                    event.target.style.animation = 'fadeIn 0.4s ease';

                    // 滚动到新添加的行
                    setTimeout(() => {{
                        event.target.scrollIntoView({{
                            behavior: 'smooth',
                            block: 'center'
                        }});
                    }}, 100);
                }}
            }});

            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(event) {{
                // Ctrl/Cmd + Enter 添加新学生
                if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {{
                    event.preventDefault();
                    addStudentBtn.click();
                }}

                // Escape 键取消当前操作
                if (event.key === 'Escape') {{
                    // 取消所有正在进行的HTMX请求
                    document.querySelectorAll('.htmx-request').forEach(el => {{
                        el.classList.remove('htmx-request');
                    }});
                }}
            }});

            // 自动保存功能（本地存储）
            let saveTimeout;
            document.addEventListener('input', function(event) {{
                if (event.target.matches('.field-input, .field-textarea')) {{
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {{
                        saveFormData();
                    }}, 1000); // 1秒后自动保存
                }}
            }});

            function saveFormData() {{
                const formData = new FormData(document.querySelector('form'));
                const data = Object.fromEntries(formData.entries());
                localStorage.setItem('studentFormData', JSON.stringify(data));

                // 显示保存提示
                showNotification('数据已自动保存', 'success');
            }}

            function loadFormData() {{
                const savedData = localStorage.getItem('studentFormData');
                if (savedData) {{
                    const data = JSON.parse(savedData);
                    Object.entries(data).forEach(([key, value]) => {{
                        const input = document.querySelector(`[name="${{key}}"]`);
                        if (input) {{
                            input.value = value;
                        }}
                    }});
                }}
            }}

            function showNotification(message, type = 'info') {{
                const notification = document.createElement('div');
                notification.className = `notification notification-${{type}}`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    z-index: 1000;
                    animation: slideInFromLeft 0.3s ease;
                    background: ${{type === 'success' ? 'hsl(142 76% 36%)' : 'hsl(217 91% 60%)'}};
                    box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.15);
                `;

                document.body.appendChild(notification);

                setTimeout(() => {{
                    notification.style.animation = 'fadeOut 0.3s ease';
                    setTimeout(() => {{
                        document.body.removeChild(notification);
                    }}, 300);
                }}, 3000);
            }}

            // 页面加载时恢复数据
            document.addEventListener('DOMContentLoaded', function() {{
                loadFormData();
            }});

            // 添加fadeOut动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeOut {{
                    from {{ opacity: 1; transform: translateX(0); }}
                    to {{ opacity: 0; transform: translateX(100%); }}
                }}
            `;
            document.head.appendChild(style);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# 用户登录页面
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """用户登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})

# 用户登录处理
@app.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    """用户登录处理"""
    user = await user_manager.authenticate_user(username, password)
    if not user:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "error": "用户名或密码错误"
        })

    # 创建访问令牌
    access_token = auth_manager.create_access_token(data={"sub": user["id"]})

    response = RedirectResponse(url="/", status_code=302)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        max_age=1800,  # 30 分钟
        httponly=True
    )

    return response

# 用户登出
@app.get("/logout")
async def logout():
    """用户登出"""
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie("access_token")
    return response

# Endpoint to add a new student row (for HTMX)
@app.post("/add-row", response_class=HTMLResponse)
async def add_row(request: Request):
    form_data = await request.form()
    current_count = int(form_data.get('studentCount', 0))
    next_index = current_count + 1
    return create_student_row_html(next_index)

# Endpoint to remove a student row (for HTMX)
@app.delete("/remove-row/{index}")
async def remove_row(index: int):
    # index参数用于路由匹配，删除操作通过HTMX在前端处理
    return HTMLResponse(content="")

# New endpoint for generating comment for a single row
@app.post("/generate-comment-for-row/{index}", response_class=HTMLResponse)
async def generate_comment_for_row(index: int, request: Request):
    form_data = await request.form()

    student_data: dict[str, str] = {}
    for field_display_name, field_internal_name, field_type, options in fields:
        input_name = f"{field_internal_name}_{index}"
        # 获取当前行的特定字段值
        value = form_data.get(input_name, "")
        student_data[field_internal_name] = value

    # 获取当前用户
    try:
        current_user = await get_current_user(request)
    except HTTPException:
        return HTMLResponse(content="请先登录", status_code=401)

    student_name_for_error = student_data.get('student_name', f'#{index}')
    generated_comment = await generate_comment_for_student(
        student_data,
        student_name_for_error,
        user_id=current_user["id"]
    )

    return HTMLResponse(content=generated_comment)

# To run the application, you would typically use:
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app=app, host="0.0.0.0", port=8000)
