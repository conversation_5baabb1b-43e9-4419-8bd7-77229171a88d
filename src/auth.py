"""
认证和用户管理模块
"""
import os
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv
from .database import get_supabase_client, get_supabase_admin_client

# 加载环境变量
load_dotenv()

# JWT 配置
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-here")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# 管理员配置
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

class AuthManager:
    """认证管理器"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token 已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的 Token",
                headers={"WWW-Authenticate": "Bearer"},
            )

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.supabase = get_supabase_admin_client()
    
    async def create_user(self, username: str, email: str, password: str) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 检查用户名和邮箱是否已存在
            existing_user = self.supabase.table("users").select("*").or_(
                f"username.eq.{username},email.eq.{email}"
            ).execute()
            
            if existing_user.data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名或邮箱已存在"
                )
            
            # 创建用户
            password_hash = AuthManager.get_password_hash(password)
            user_data = {
                "username": username,
                "email": email,
                "password_hash": password_hash,
                "is_active": True
            }
            
            result = self.supabase.table("users").insert(user_data).execute()
            
            if result.data:
                user = result.data[0]
                # 不返回密码哈希
                user.pop("password_hash", None)
                return user
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="创建用户失败"
                )
                
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建用户时发生错误: {str(e)}"
            )
    
    async def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """验证用户"""
        try:
            result = self.supabase.table("users").select("*").eq("username", username).execute()
            
            if not result.data:
                return None
            
            user = result.data[0]
            if not AuthManager.verify_password(password, user["password_hash"]):
                return None
            
            # 更新最后登录时间
            self.supabase.table("users").update({
                "last_login": datetime.utcnow().isoformat()
            }).eq("id", user["id"]).execute()
            
            # 不返回密码哈希
            user.pop("password_hash", None)
            return user
            
        except Exception as e:
            print(f"用户认证错误: {e}")
            return None
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """根据 ID 获取用户"""
        try:
            result = self.supabase.table("users").select("*").eq("id", user_id).execute()
            
            if result.data:
                user = result.data[0]
                user.pop("password_hash", None)
                return user
            return None
            
        except Exception as e:
            print(f"获取用户错误: {e}")
            return None
    
    async def get_all_users(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """获取所有用户"""
        try:
            result = self.supabase.table("users").select(
                "id, username, email, is_active, created_at, updated_at, last_login"
            ).range(offset, offset + limit - 1).execute()
            
            return {
                "users": result.data,
                "total": len(result.data)
            }
            
        except Exception as e:
            print(f"获取用户列表错误: {e}")
            return {"users": [], "total": 0}
    
    async def update_user(self, user_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新用户"""
        try:
            # 如果包含密码，需要加密
            if "password" in update_data:
                update_data["password_hash"] = AuthManager.get_password_hash(update_data.pop("password"))
            
            result = self.supabase.table("users").update(update_data).eq("id", user_id).execute()
            
            if result.data:
                user = result.data[0]
                user.pop("password_hash", None)
                return user
            return None
            
        except Exception as e:
            print(f"更新用户错误: {e}")
            return None
    
    async def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            result = self.supabase.table("users").delete().eq("id", user_id).execute()
            return len(result.data) > 0
            
        except Exception as e:
            print(f"删除用户错误: {e}")
            return False

# 依赖注入函数
async def get_current_user(request: Request) -> Dict[str, Any]:
    """获取当前用户"""
    # 尝试从 Cookie 获取 token
    token = request.cookies.get("access_token")
    if token and token.startswith("Bearer "):
        token = token[7:]  # 移除 "Bearer " 前缀

    if not token:
        # 尝试从 Authorization header 获取
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    payload = AuthManager.verify_token(token)

    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的 Token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_manager_instance = UserManager()
    user = await user_manager_instance.get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user

async def verify_admin_credentials(username: str, password: str) -> bool:
    """验证管理员凭据"""
    return username == ADMIN_USERNAME and password == ADMIN_PASSWORD

# 创建全局实例
auth_manager = AuthManager()
user_manager = UserManager()
