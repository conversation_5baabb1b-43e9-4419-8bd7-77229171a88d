"""
数据库配置和初始化
"""
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# Supabase 配置
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

# 创建 Supabase 客户端
def get_supabase_client() -> Client:
    """获取 Supabase 客户端"""
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL 和 Key 必须在环境变量中设置")
    return create_client(SUPABASE_URL, SUPABASE_KEY)

def get_supabase_admin_client() -> Client:
    """获取 Supabase 管理员客户端（使用 service key）"""
    if not SUPABASE_URL or not SUPABASE_SERVICE_KEY:
        raise ValueError("Supabase URL 和 Service Key 必须在环境变量中设置")
    return create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

# 数据库表结构 SQL
DATABASE_SCHEMA = """
-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE
);

-- Token 使用记录表
CREATE TABLE IF NOT EXISTS token_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tokens_used INTEGER NOT NULL,
    api_call_type VARCHAR(50) NOT NULL DEFAULT 'comment_generation',
    request_data JSONB,
    response_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_created_at ON token_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略 (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 用户只能查看和更新自己的信息
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Token 使用记录策略
CREATE POLICY "Users can view own token usage" ON token_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can insert token usage" ON token_usage
    FOR INSERT WITH CHECK (true);

-- 会话策略
CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage sessions" ON user_sessions
    FOR ALL WITH CHECK (true);
"""

async def init_database():
    """初始化数据库表结构"""
    try:
        admin_client = get_supabase_admin_client()

        # 简单测试连接
        try:
            # 尝试查询用户表，如果不存在会报错
            result = admin_client.table("users").select("id").limit(1).execute()
            print("用户表已存在")
        except Exception:
            print("用户表不存在，需要手动创建数据库表")
            print("请在 Supabase 控制台的 SQL 编辑器中执行以下 SQL:")
            print("=" * 50)
            print(DATABASE_SCHEMA)
            print("=" * 50)
            return False

        print("数据库连接成功")
        return True
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print("请检查你的 Supabase 配置")
        return False

if __name__ == "__main__":
    import asyncio
    asyncio.run(init_database())
