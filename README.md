# 学生评语生成系统

基于 AI 的学生评语生成和管理系统，集成了完整的后台管理功能和用户认证系统。

## 功能特性

### 🎯 核心功能
- **AI 评语生成**: 基于 Deepseek API 的智能学生评语生成
- **用户认证系统**: 完整的用户登录、注册和权限管理
- **后台管理系统**: 管理员可以管理用户账号和查看使用统计
- **Token 使用追踪**: 详细记录每个用户的 API 调用和 Token 消耗

### 🔐 安全特性
- 基于 JWT 的用户认证
- 管理员账密环境变量控制
- Supabase 行级安全策略 (RLS)
- 密码加密存储

### 📊 管理功能
- 用户账号管理（创建、编辑、删除、禁用）
- Token 使用统计和趋势分析
- 用户使用排行和详细记录
- 实时数据仪表板

## 技术栈

- **后端**: FastAPI + Python 3.13
- **数据库**: Supabase (PostgreSQL)
- **前端**: HTML + Tailwind CSS + HTMX + Alpine.js
- **认证**: JWT + Passlib
- **AI API**: Deepseek API

## 快速开始

### 1. 环境准备

确保你已经安装了 Python 3.13+ 和 uv 包管理器。

```bash
# 克隆项目
git clone <repository-url>
cd easy-comments

# 安装依赖
uv sync
```

### 2. 配置环境变量

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下变量：

```env
# Deepseek API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# Supabase 配置
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# JWT 配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=False
```

### 3. 初始化数据库

运行数据库初始化脚本：

```bash
python init_db.py
```

### 4. 启动应用

```bash
# 开发模式
uv run uvicorn src.app:app --reload --host 0.0.0.0 --port 8000

# 或者使用 Python 直接运行
python -m uvicorn src.app:app --reload
```

应用将在 http://localhost:8000 启动。

## 使用指南

### 管理员操作

1. **登录管理后台**: 访问 http://localhost:8000/admin/
2. **创建用户账号**: 在用户管理页面创建新用户
3. **查看使用统计**: 在 Token 统计页面查看使用情况
4. **管理用户**: 编辑、禁用或删除用户账号

### 普通用户操作

1. **用户登录**: 访问 http://localhost:8000/login
2. **生成评语**: 填写学生信息，点击生成按钮
3. **查看历史**: 管理员可以查看你的使用记录

## 项目结构

```
easy-comments/
├── src/
│   ├── app.py              # 主应用文件
│   ├── auth.py             # 认证和用户管理
│   ├── database.py         # 数据库配置
│   ├── token_tracker.py    # Token 使用追踪
│   └── admin_routes.py     # 管理员路由
├── templates/
│   ├── admin/              # 管理后台模板
│   │   ├── base.html
│   │   ├── login.html
│   │   ├── dashboard.html
│   │   ├── users.html
│   │   └── ...
│   └── login.html          # 用户登录页面
├── init_db.py              # 数据库初始化脚本
├── .env.example            # 环境变量模板
├── pyproject.toml          # 项目配置
└── README.md               # 项目文档
```

## 数据库设计

### 用户表 (users)
- id: UUID 主键
- username: 用户名
- email: 邮箱
- password_hash: 密码哈希
- is_active: 是否激活
- created_at/updated_at: 时间戳
- last_login: 最后登录时间

### Token 使用记录表 (token_usage)
- id: UUID 主键
- user_id: 用户 ID (外键)
- tokens_used: 使用的 Token 数量
- api_call_type: API 调用类型
- request_data/response_data: 请求和响应数据
- created_at: 创建时间

### 用户会话表 (user_sessions)
- id: UUID 主键
- user_id: 用户 ID (外键)
- session_token: 会话令牌
- expires_at: 过期时间
- created_at: 创建时间

## API 接口

### 用户认证
- `GET /login` - 登录页面
- `POST /login` - 用户登录
- `GET /logout` - 用户登出

### 主要功能
- `GET /` - 主页面（需要认证）
- `POST /generate-comment-for-row/{index}` - 生成评语
- `POST /add-row` - 添加学生行
- `DELETE /remove-row/{index}` - 删除学生行

### 管理后台
- `GET /admin/` - 管理员登录
- `GET /admin/dashboard` - 仪表板
- `GET /admin/users` - 用户管理
- `GET /admin/token-usage` - Token 统计

## 开发说明

### 添加新功能

1. **新增 API 路由**: 在 `src/app.py` 或创建新的路由文件
2. **数据库操作**: 在相应的模块中添加数据库操作函数
3. **前端页面**: 在 `templates/` 目录下添加 HTML 模板
4. **权限控制**: 使用 `Depends(get_current_user)` 进行认证

### 自定义配置

- **字段配置**: 修改 `src/app.py` 中的 `fields` 列表
- **样式定制**: 修改模板中的 Tailwind CSS 类
- **API 集成**: 替换 Deepseek API 为其他 AI 服务

## 部署指南

### Docker 部署

```dockerfile
FROM python:3.13-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8000
CMD ["uv", "run", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 环境变量配置

生产环境中，确保设置以下环境变量：
- 使用强密码作为管理员密码
- 生成安全的 JWT 密钥
- 配置正确的 Supabase 凭据
- 设置 `DEBUG=False`

## 故障排除

### 常见问题

1. **数据库连接失败**: 检查 Supabase 配置和网络连接
2. **认证失败**: 确认 JWT 密钥配置正确
3. **API 调用失败**: 检查 Deepseek API 密钥和配额
4. **权限错误**: 确认 Supabase RLS 策略配置

### 日志查看

应用日志会输出到控制台，包含：
- 数据库操作结果
- API 调用状态
- 认证和授权信息
- 错误详情

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建 Issue 或联系项目维护者。