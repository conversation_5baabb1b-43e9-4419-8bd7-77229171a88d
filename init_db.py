#!/usr/bin/env python3
"""
数据库初始化脚本
运行此脚本来初始化 Supabase 数据库表结构
"""
import asyncio
import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database import init_database

async def main():
    """主函数"""
    print("开始初始化数据库...")
    
    try:
        success = await init_database()
        if success:
            print("✅ 数据库初始化成功！")
            print("\n接下来的步骤：")
            print("1. 复制 .env.example 为 .env")
            print("2. 在 .env 文件中配置你的环境变量")
            print("3. 运行应用: python -m uvicorn src.app:app --reload")
        else:
            print("❌ 数据库初始化失败")
            print("请检查你的 Supabase 配置")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 初始化过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
