<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}后台管理系统{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    {% if request.url.path != '/admin/' %}
    <!-- 导航栏 -->
    <nav class="bg-blue-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold">学生评语系统 - 后台管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/admin/dashboard" class="hover:text-blue-200">
                        <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                    </a>
                    <a href="/admin/users" class="hover:text-blue-200">
                        <i class="fas fa-users mr-2"></i>用户管理
                    </a>
                    <a href="/admin/token-usage" class="hover:text-blue-200">
                        <i class="fas fa-chart-bar mr-2"></i>Token 统计
                    </a>
                    <a href="/admin/logout" class="hover:text-blue-200">
                        <i class="fas fa-sign-out-alt mr-2"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- 主要内容 -->
    <main class="{% if request.url.path != '/admin/' %}max-w-7xl mx-auto py-6 px-4{% endif %}">
        {% block content %}{% endblock %}
    </main>

    <!-- 通用脚本 -->
    <script>
        // 确认删除对话框
        function confirmDelete(message = '确定要删除吗？') {
            return confirm(message);
        }

        // 显示成功消息
        function showSuccess(message) {
            alert(message);
        }

        // 显示错误消息
        function showError(message) {
            alert(message);
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
