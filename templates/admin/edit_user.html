{% extends "admin/base.html" %}

{% block title %}编辑用户 - 后台管理系统{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
    <!-- 页面标题 -->
    <div class="border-b border-gray-200 pb-4">
        <h1 class="text-2xl font-bold text-gray-900">编辑用户</h1>
        <p class="text-gray-600">修改用户 {{ user.username }} 的信息</p>
    </div>

    <!-- 表单 -->
    <div class="bg-white shadow rounded-lg">
        <form method="POST" action="/admin/users/{{ user.id }}/edit" class="space-y-6 p-6">
            {% if error %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ error }}</span>
            </div>
            {% endif %}

            <div class="grid grid-cols-1 gap-6">
                <!-- 用户名 -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">
                        用户名 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="text" name="username" id="username" required
                               value="{{ user.username }}"
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请输入用户名">
                    </div>
                </div>

                <!-- 邮箱 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                        邮箱地址 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="email" name="email" id="email" required
                               value="{{ user.email }}"
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请输入邮箱地址">
                    </div>
                </div>

                <!-- 账号状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        账号状态
                    </label>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="is_active" value="true" 
                                   {% if user.is_active %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">账号激活</span>
                        </label>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        取消勾选将禁用该用户账号
                    </p>
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        新密码
                    </label>
                    <div class="mt-1">
                        <input type="password" name="password" id="password"
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="留空则不修改密码">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        如需修改密码，请输入新密码，否则留空
                    </p>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">
                        确认新密码
                    </label>
                    <div class="mt-1">
                        <input type="password" name="confirm_password" id="confirm_password"
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请再次输入新密码">
                    </div>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="bg-gray-50 p-4 rounded-md">
                <h4 class="text-sm font-medium text-gray-900 mb-2">用户信息</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="text-gray-500">用户 ID:</span>
                        <span class="text-gray-900">{{ user.id }}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">注册时间:</span>
                        <span class="text-gray-900">{{ user.created_at[:19] }}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">最后更新:</span>
                        <span class="text-gray-900">{{ user.updated_at[:19] if user.updated_at else '未更新' }}</span>
                    </div>
                    <div>
                        <span class="text-gray-500">最后登录:</span>
                        <span class="text-gray-900">{{ user.last_login[:19] if user.last_login else '从未登录' }}</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="/admin/users" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-times mr-2"></i>取消
                </a>
                <a href="/admin/token-usage/{{ user.id }}" 
                   class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                    <i class="fas fa-chart-bar mr-2"></i>Token 统计
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>保存修改
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 密码确认验证
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password && password !== confirmPassword) {
            this.setCustomValidity('密码不匹配');
        } else {
            this.setCustomValidity('');
        }
    });

    // 表单提交验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (password && password !== confirmPassword) {
            e.preventDefault();
            alert('密码和确认密码不匹配');
            return false;
        }
        
        if (password && password.length < 6) {
            e.preventDefault();
            alert('密码长度至少 6 位');
            return false;
        }
    });
</script>
{% endblock %}
