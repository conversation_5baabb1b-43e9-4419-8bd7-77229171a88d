{% extends "admin/base.html" %}

{% block title %}仪表板 - 后台管理系统{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="border-b border-gray-200 pb-4">
        <h1 class="text-2xl font-bold text-gray-900">仪表板</h1>
        <p class="text-gray-600">系统概览和统计信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 用户总数 -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-blue-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                用户总数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ users_count }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Token 总使用量 -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-coins text-green-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Token 总使用量
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(token_stats.total_tokens) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- API 调用总数 -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                API 调用总数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(token_stats.total_calls) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活跃用户数 -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-check text-orange-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                活跃用户数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ token_stats.total_users }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近用户和使用趋势 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近注册用户 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-user-plus mr-2"></i>最近注册用户
                </h3>
                <div class="space-y-3">
                    {% for user in recent_users %}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-blue-600 text-sm"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ user.username }}</p>
                                <p class="text-xs text-gray-500">{{ user.email }}</p>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ user.created_at[:10] }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="mt-4">
                    <a href="/admin/users" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        查看所有用户 <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- 使用趋势 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    <i class="fas fa-chart-area mr-2"></i>7天使用趋势
                </h3>
                <div class="space-y-2">
                    {% for date in trends.dates[-7:] %}
                    {% set day_data = trends.daily_trends[date] %}
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ date }}</span>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-900">{{ day_data.tokens }} tokens</span>
                            <span class="text-xs text-gray-500">{{ day_data.calls }} 次调用</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="mt-4">
                    <a href="/admin/token-usage" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                        查看详细统计 <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-bolt mr-2"></i>快速操作
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="/admin/users/create" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-user-plus mr-2"></i>创建用户
                </a>
                <a href="/admin/users" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-users mr-2"></i>管理用户
                </a>
                <a href="/admin/token-usage" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-chart-bar mr-2"></i>Token 统计
                </a>
                <a href="/" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-home mr-2"></i>返回主页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
