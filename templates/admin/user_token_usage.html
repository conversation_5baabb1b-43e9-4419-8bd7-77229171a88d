{% extends "admin/base.html" %}

{% block title %}{{ user.username }} - Token 使用详情{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 页面标题和用户信息 -->
    <div class="border-b border-gray-200 pb-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ user.username }} - Token 使用详情</h1>
                <p class="text-gray-600">{{ user.email }}</p>
            </div>
            <div class="flex items-center space-x-2">
                <label for="days" class="text-sm text-gray-700">统计周期:</label>
                <select id="days" onchange="changePeriod(this.value)" 
                        class="border-gray-300 rounded-md text-sm">
                    <option value="7" {% if days == 7 %}selected{% endif %}>7天</option>
                    <option value="30" {% if days == 30 %}selected{% endif %}>30天</option>
                    <option value="90" {% if days == 90 %}selected{% endif %}>90天</option>
                </select>
            </div>
        </div>
        <div class="mt-2">
            <a href="/admin/users" class="text-blue-600 hover:text-blue-500 text-sm">
                <i class="fas fa-arrow-left mr-1"></i>返回用户列表
            </a>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-coins text-green-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                总 Token 使用量
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(usage_stats.total_tokens) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-blue-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                总调用次数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(usage_stats.total_calls) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calculator text-purple-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                平均每次调用
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:.1f}".format(usage_stats.average_tokens_per_call) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar text-orange-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                统计周期
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ days }} 天
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 每日使用趋势 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-area mr-2"></i>每日使用趋势
            </h3>
            <div class="space-y-2">
                {% set sorted_dates = usage_stats.daily_usage.keys() | list | sort %}
                {% for date in sorted_dates[-14:] %}
                {% set day_data = usage_stats.daily_usage[date] %}
                <div class="flex items-center">
                    <div class="w-20 text-sm text-gray-600">{{ date[5:] }}</div>
                    <div class="flex-1 mx-4">
                        <div class="bg-gray-200 rounded-full h-4 relative">
                            {% set max_tokens = usage_stats.daily_usage.values() | map(attribute='tokens') | max %}
                            {% if max_tokens > 0 %}
                            <div class="bg-blue-600 h-4 rounded-full" 
                                 style="width: {{ (day_data.tokens / max_tokens * 100) | round(1) }}%"></div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="w-32 text-right text-sm">
                        <span class="text-gray-900 font-medium">{{ day_data.tokens }}</span>
                        <span class="text-gray-500">tokens</span>
                    </div>
                    <div class="w-20 text-right text-xs text-gray-500">
                        {{ day_data.calls }} 次
                    </div>
                </div>
                {% endfor %}
                
                {% if not usage_stats.daily_usage %}
                <div class="text-center py-8">
                    <i class="fas fa-chart-area text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">该时间段内无使用数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 按类型统计 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-pie-chart mr-2"></i>按调用类型统计
            </h3>
            <div class="space-y-3">
                {% for call_type, type_data in usage_stats.usage_by_type.items() %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-cog text-green-600 text-sm"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ call_type }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ "{:,}".format(type_data.tokens) }} tokens</p>
                        <p class="text-xs text-gray-500">{{ type_data.calls }} 次调用</p>
                    </div>
                </div>
                {% endfor %}
                
                {% if not usage_stats.usage_by_type %}
                <div class="text-center py-8">
                    <i class="fas fa-pie-chart text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">暂无分类数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 最近调用记录 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-history mr-2"></i>最近调用记录
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                时间
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                类型
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Token 使用量
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for record in usage_stats.recent_records %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ record.created_at[:19] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ record.api_call_type }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ record.tokens_used }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                {% if not usage_stats.recent_records %}
                <div class="text-center py-8">
                    <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">暂无调用记录</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-between">
        <a href="/admin/users/{{ user.id }}/edit" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-edit mr-2"></i>编辑用户
        </a>
        <a href="/admin/token-usage" 
           class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
            <i class="fas fa-chart-bar mr-2"></i>查看全局统计
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function changePeriod(days) {
        window.location.href = `/admin/token-usage/{{ user.id }}?days=${days}`;
    }
</script>
{% endblock %}
