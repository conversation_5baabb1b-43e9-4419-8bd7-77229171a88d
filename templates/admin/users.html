{% extends "admin/base.html" %}

{% block title %}用户管理 - 后台管理系统{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
            <p class="text-gray-600">管理系统用户账号</p>
        </div>
        <a href="/admin/users/create" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <i class="fas fa-plus mr-2"></i>创建用户
        </a>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                用户列表 (共 {{ total }} 个用户)
            </h3>
        </div>
        
        {% if users %}
        <ul class="divide-y divide-gray-200">
            {% for user in users %}
            <li class="px-4 py-4 sm:px-6 hover:bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <div class="flex items-center">
                                <p class="text-sm font-medium text-gray-900">{{ user.username }}</p>
                                {% if not user.is_active %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    已禁用
                                </span>
                                {% else %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    活跃
                                </span>
                                {% endif %}
                            </div>
                            <p class="text-sm text-gray-500">{{ user.email }}</p>
                            <div class="text-xs text-gray-400 mt-1">
                                <span>注册时间: {{ user.created_at[:10] }}</span>
                                {% if user.last_login %}
                                <span class="ml-4">最后登录: {{ user.last_login[:10] }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <a href="/admin/token-usage/{{ user.id }}" 
                           class="text-blue-600 hover:text-blue-900 text-sm">
                            <i class="fas fa-chart-bar mr-1"></i>Token 统计
                        </a>
                        <a href="/admin/users/{{ user.id }}/edit" 
                           class="text-indigo-600 hover:text-indigo-900 text-sm">
                            <i class="fas fa-edit mr-1"></i>编辑
                        </a>
                        <form method="POST" action="/admin/users/{{ user.id }}/delete" 
                              class="inline" onsubmit="return confirmDelete('确定要删除用户 {{ user.username }} 吗？')">
                            <button type="submit" class="text-red-600 hover:text-red-900 text-sm">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </form>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        {% else %}
        <div class="px-4 py-8 text-center">
            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-500">暂无用户</p>
            <a href="/admin/users/create" 
               class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>创建第一个用户
            </a>
        </div>
        {% endif %}
    </div>

    <!-- 分页 -->
    {% if total_pages > 1 %}
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if page > 1 %}
            <a href="/admin/users?page={{ page - 1 }}&limit={{ limit }}" 
               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </a>
            {% endif %}
            {% if page < total_pages %}
            <a href="/admin/users?page={{ page + 1 }}&limit={{ limit }}" 
               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </a>
            {% endif %}
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    显示第 <span class="font-medium">{{ (page - 1) * limit + 1 }}</span> 到 
                    <span class="font-medium">{{ min(page * limit, total) }}</span> 条，
                    共 <span class="font-medium">{{ total }}</span> 条记录
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page > 1 %}
                    <a href="/admin/users?page={{ page - 1 }}&limit={{ limit }}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    {% endif %}
                    
                    {% for p in range(1, total_pages + 1) %}
                        {% if p == page %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ p }}
                        </span>
                        {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                        <a href="/admin/users?page={{ p }}&limit={{ limit }}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ p }}
                        </a>
                        {% elif p == 4 or p == total_pages - 3 %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page < total_pages %}
                    <a href="/admin/users?page={{ page + 1 }}&limit={{ limit }}" 
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
