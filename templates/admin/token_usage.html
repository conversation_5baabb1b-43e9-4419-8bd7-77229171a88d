{% extends "admin/base.html" %}

{% block title %}Token 使用统计 - 后台管理系统{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 页面标题和时间范围选择 -->
    <div class="flex justify-between items-center border-b border-gray-200 pb-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Token 使用统计</h1>
            <p class="text-gray-600">查看系统 Token 使用情况和趋势</p>
        </div>
        <div class="flex items-center space-x-2">
            <label for="days" class="text-sm text-gray-700">统计周期:</label>
            <select id="days" onchange="changePeriod(this.value)" 
                    class="border-gray-300 rounded-md text-sm">
                <option value="7" {% if days == 7 %}selected{% endif %}>7天</option>
                <option value="30" {% if days == 30 %}selected{% endif %}>30天</option>
                <option value="90" {% if days == 90 %}selected{% endif %}>90天</option>
            </select>
        </div>
    </div>

    <!-- 总体统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-coins text-green-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                总 Token 使用量
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(stats.total_tokens) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-blue-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                总调用次数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:,}".format(stats.total_calls) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users text-purple-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                活跃用户数
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ stats.total_users }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calculator text-orange-600 text-2xl"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                平均每用户
                            </dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ "{:.0f}".format(stats.average_tokens_per_user) }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用趋势图 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-chart-area mr-2"></i>使用趋势 ({{ days }} 天)
            </h3>
            <div class="space-y-2">
                {% for date in trends.dates[-14:] %}
                {% set day_data = trends.daily_trends[date] %}
                <div class="flex items-center">
                    <div class="w-20 text-sm text-gray-600">{{ date[5:] }}</div>
                    <div class="flex-1 mx-4">
                        <div class="bg-gray-200 rounded-full h-4 relative">
                            {% set max_tokens = trends.daily_trends.values() | map(attribute='tokens') | max %}
                            {% if max_tokens > 0 %}
                            <div class="bg-blue-600 h-4 rounded-full" 
                                 style="width: {{ (day_data.tokens / max_tokens * 100) | round(1) }}%"></div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="w-32 text-right text-sm">
                        <span class="text-gray-900 font-medium">{{ day_data.tokens }}</span>
                        <span class="text-gray-500">tokens</span>
                    </div>
                    <div class="w-20 text-right text-xs text-gray-500">
                        {{ day_data.calls }} 次
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 用户使用排行 -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-trophy mr-2"></i>用户使用排行
            </h3>
            <div class="space-y-3">
                {% for user_id, user_data in stats.user_stats.items() %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 text-sm"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">
                                {{ user_data.username if user_data.username else '未知用户' }}
                            </p>
                            <p class="text-xs text-gray-500">{{ user_data.email if user_data.email else user_id }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">{{ "{:,}".format(user_data.total_tokens) }} tokens</p>
                        <p class="text-xs text-gray-500">{{ user_data.total_calls }} 次调用</p>
                    </div>
                    <div class="ml-4">
                        <a href="/admin/token-usage/{{ user_id }}" 
                           class="text-blue-600 hover:text-blue-900 text-sm">
                            <i class="fas fa-chart-bar mr-1"></i>详情
                        </a>
                    </div>
                </div>
                {% endfor %}
                
                {% if not stats.user_stats %}
                <div class="text-center py-8">
                    <i class="fas fa-chart-bar text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">暂无使用数据</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function changePeriod(days) {
        window.location.href = `/admin/token-usage?days=${days}`;
    }
</script>
{% endblock %}
