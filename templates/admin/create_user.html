{% extends "admin/base.html" %}

{% block title %}创建用户 - 后台管理系统{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto space-y-6">
    <!-- 页面标题 -->
    <div class="border-b border-gray-200 pb-4">
        <h1 class="text-2xl font-bold text-gray-900">创建用户</h1>
        <p class="text-gray-600">为系统创建新的用户账号</p>
    </div>

    <!-- 表单 -->
    <div class="bg-white shadow rounded-lg">
        <form method="POST" action="/admin/users/create" class="space-y-6 p-6">
            {% if error %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ error }}</span>
            </div>
            {% endif %}

            <div class="grid grid-cols-1 gap-6">
                <!-- 用户名 -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">
                        用户名 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="text" name="username" id="username" required
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请输入用户名">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        用户名用于登录，只能包含字母、数字和下划线
                    </p>
                </div>

                <!-- 邮箱 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                        邮箱地址 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="email" name="email" id="email" required
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请输入邮箱地址">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        请输入有效的邮箱地址
                    </p>
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        密码 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="password" name="password" id="password" required
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请输入密码">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        密码长度至少 6 位，建议包含字母、数字和特殊字符
                    </p>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">
                        确认密码 <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="password" name="confirm_password" id="confirm_password" required
                               class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                               placeholder="请再次输入密码">
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="/admin/users" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-times mr-2"></i>取消
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>创建用户
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 密码确认验证
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('密码不匹配');
        } else {
            this.setCustomValidity('');
        }
    });

    // 表单提交验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('密码和确认密码不匹配');
            return false;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            alert('密码长度至少 6 位');
            return false;
        }
    });
</script>
{% endblock %}
